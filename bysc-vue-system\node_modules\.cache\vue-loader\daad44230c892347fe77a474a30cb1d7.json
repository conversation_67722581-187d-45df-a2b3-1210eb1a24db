{"remainingRequest": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\bysc_system\\views\\tenant\\index.vue?vue&type=template&id=06b972cf&scoped=true", "dependencies": [{"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\bysc_system\\views\\tenant\\index.vue", "mtime": 1753861034618}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745221302709}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745221302709}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1745221307620}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1745221315417}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745221302709}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745221307761}], "contextDependencies": [], "result": ["\n<div>\n  <el-row>\n    <el-col :span=\"24\">\n      <Grid\n        api=\"tenant/tenant-page\"\n        :event-bus=\"searchEventBus\"\n        :search-params=\"searchForm\"\n        :newcolumn=\"columns\"\n        @datas=\"getDatas\"\n        @columnChange=\"getColumn\"\n        ref=\"grid\"\n      >\n        <div slot=\"search\">\n          <el-form\n            :inline=\"true\"\n            :model=\"searchForm\"\n            class=\"demo-form-inline\"\n          >\n            <el-form-item label=\"租户编码\">\n              <el-input\n                style=\"width: 200px;margin:0 10px 0 0;\"\n                v-model.trim=\"searchForm.tenantCode\"\n                size=\"small\"\n                placeholder=\"请输入租户编码\"\n              ></el-input>\n            </el-form-item>\n            <el-form-item label=\"租户名称\">\n              <el-input\n                style=\"width: 200px;margin:0 10px 0 0;\"\n                v-model.trim=\"searchForm.tenantName\"\n                size=\"small\"\n                placeholder=\"请输入租户名称\"\n              ></el-input>\n            </el-form-item>\n\n            <el-form-item label=\"是否选中\">\n              <el-select\n                size=\"small\"\n                clearable\n                @keydown.enter.native.prevent=\"searchTable\"\n                v-model=\"searchForm.isSelected\"\n                placeholder=\"请选择是否选中\"\n              >\n                <el-option :label=\"'是'\" :value=\"1\"> </el-option>\n                <el-option :label=\"'否'\" :value=\"2\"> </el-option>\n              </el-select>\n            </el-form-item>\n            <el-form-item>\n              <el-button\n                size=\"small\"\n                type=\"primary\"\n                style=\"margin: 0 0 0 10px\"\n                @click=\"searchTable\"\n                >搜索</el-button>\n              <el-button size=\"small\" @click=\"resetTable\">重置</el-button>\n            </el-form-item>\n          </el-form>\n        </div>\n        <div slot=\"action\">\n          <el-button v-permission=\"'tenant_add'\" size=\"small\" type=\"primary\" @click=\"handleAdd\">新建租户</el-button>\n          <el-button v-permission=\"'tenant_batchDel'\" size=\"small\" @click=\"batchDelete\">删除租户</el-button>\n        </div>\n        <el-table slot=\"table\" slot-scope=\"{}\" v-loading=\"tableLoading\" ref=\"multipleTable\" :data=\"tableData\" stripe style=\"width: 100%\">\n          <el-table-column fixed=\"left\" :align=\"'center'\" type=\"selection\" width=\"55\">\n          </el-table-column>\n          <el-table-column fixed=\"left\" :align=\"'center'\" label=\"序号\" type=\"index\" width=\"50\">\n          </el-table-column>\n          <template v-for=\"(item, index) in columns\">\n            <el-table-column\n              v-if=\"item.slot === 'isSelected'\"\n              :show-overflow-tooltip=\"true\"\n              :align=\"item.align ? item.align : 'center'\"\n              :key=\"index\"\n              :prop=\"item.key\"\n              :label=\"item.title\"\n              min-width=\"100\"\n            >\n              <template slot-scope=\"scope\">\n               {{scope.row.isSelected === 1  ? \"是\":'否'}}\n              </template>\n            </el-table-column>\n            <el-table-column\n              v-else-if=\"item.slot\"\n              :show-overflow-tooltip=\"true\"\n              :align=\"item.align ? item.align : 'center'\"\n              :key=\"index\"\n              :prop=\"item.key\"\n              :label=\"item.title\"\n              min-width=\"180\"\n            >\n              <template slot-scope=\"scope\">\n                <el-tag :type=\"scope.row[item.slot]?'success':'danger'\">{{scope.row[item.slot]?'启用':'禁用'}}</el-tag>\n              </template>\n            </el-table-column>\n            <el-table-column\n              v-else\n              :show-overflow-tooltip=\"true\"\n              :key=\"item.key\"\n              :prop=\"item.key\"\n              :label=\"item.title\"\n              :min-width=\"item.width ? item.width : '150'\"\n              :align=\"item.align ? item.align : 'center'\"\n            >\n            </el-table-column>\n          </template>\n          <el-table-column\n            fixed=\"right\"\n            align=\"center\"\n            label=\"操作\"\n            type=\"action\"\n            width=\"200\"\n          >\n            <template slot-scope=\"scope\">\n              <template>\n                <el-button\n                  v-permission=\"'tenant_edit'\"\n                  style=\"margin-right:6px\"\n                  @click=\"handleEdit(scope.row)\"\n                  type=\"text\"\n                  size=\"small\"\n                  >编辑</el-button\n                >\n              </template>\n\n              <template>\n\n                <el-button\n                  v-permission=\"'tenant_admin_manage'\"\n                  style=\"margin-right:6px\"\n                  @click=\"handleAdminManage(scope.row)\"\n                  type=\"text\"\n                  size=\"small\"\n                  >维护管理员</el-button\n                >\n              </template>\n\n              <template>\n                <el-button\n                  v-permission=\"'tenant_del'\"\n                  type=\"text\"\n                  size=\"small\"\n                  @click=\"handleDelete(scope.row.id)\"\n                >删除</el-button>\n              </template>\n            </template>\n          </el-table-column>\n        </el-table>\n      </Grid>\n    </el-col>\n  </el-row>\n  <el-drawer\n    size=\"50%\"\n    :title=\"drawerName\"\n    :visible.sync=\"drawer\"\n    :direction=\"direction\"\n    :wrapperClosable=\"false\"\n  >\n    <TenantForm\n      v-model=\"tenantRuleForm\"\n      :is-edit=\"isEditMode\"\n      :loading=\"submitLoading\"\n      @submit=\"handleFormSubmit\"\n      @cancel=\"handleFormCancel\"\n      ref=\"tenantForm\"\n    />\n  </el-drawer>\n\n  <!-- 维护管理员弹窗 -->\n  <AdminManageDialog\n    :visible.sync=\"adminManageVisible\"\n    :tenant-info=\"currentTenant\"\n  />\n</div>\n", null]}